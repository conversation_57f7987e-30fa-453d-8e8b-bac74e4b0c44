import coverImage from './../../../assets/cover.png';
import logo from './../../../assets/LogoNew.png';

const DeepSightCoverPage = () => {
  return (
    <div className="min-h-screen p-5">
      {/* Main Container */}
      <div className="max-w-6xl mx-auto bg-white flex flex-col min-h-screen p-10 h-[297mm]">



        {/* Main Content Area */}
        <div className='flex flex-col justify-center h-full relative'>

          {/* Top Section with Current Financial State Analysis */}
          <div className="relative mb-12">
            <img src={coverImage} alt="Cover Page" className="w-full h-auto" />
          </div>

          {/* Middle Content Section */}
          <div className="pl-24 mb-8">
            <div className="mb-8">
              <p
                className="text-3xl font-light text-gray-600 mb-2 font-medium"
              >
                Prepared For:
              </p>
              <h2
                className="text-8xl font-bold text-gray-800 mb-2"
              >
                Acme Print
              </h2>
            </div>

            <div>
              <p
                className="text-6xl font-bold text-teal-600 font-light "
              >
                January 2025
              </p>
            </div>
          </div>

          {/* Bottom Section with Logo */}
          <div className='flex justify-end pr-24 mt-20'>
            <div className=" mt-auto flex flex-col justify-end h-1/4">
              <div className="">
                <p
                  className="text-2xl text-gray-500 mb-2"
                >
                  Prepared By:
                </p>
              </div>
              <div className='flex items-center text-right'>
                <img src={logo} alt="Logo" className="w-20 h-auto" />
                <p className='text-2xl text-gray-800 font-bold'>VALISIGHTS</p>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default DeepSightCoverPage;