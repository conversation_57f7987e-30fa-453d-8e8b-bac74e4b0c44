import React from 'react';

const ProfitLoss13MonthDashboard = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  subHeaderTextStyle = {},
  contentTextStyle = {},
  reportData = null // Add reportData prop to receive API data
}) => {
  // Extract background color from headerTextStyle for table header
  const headerBgColor = headerTextStyle.color || '#20b2aa';

  // Function to transform API data into the required format
  const transformApiData = (apiData) => {
    if (!apiData || !apiData.profitAndLossMonthsTrailing) {
      return { months: [], tableData: [] };
    }

    const rawData = apiData.profitAndLossMonthsTrailing;
    
    // Extract months from the first data item (excluding account_type and account_name)
    const months = rawData.length > 0 
      ? Object.keys(rawData[0]).filter(key => key !== 'account_type' && key !== 'account_name')
      : [];

    // Group data by account_type
    const groupedData = rawData.reduce((acc, item) => {
      const accountType = item.account_type;
      if (!acc[accountType]) {
        acc[accountType] = [];
      }
      acc[accountType].push(item);
      return acc;
    }, {});

    // Transform grouped data into table format
    const tableData = [];
    
    Object.keys(groupedData).forEach(accountType => {
      // Add header row for account type
      tableData.push({
        isHeader: true,
        category: accountType
      });

      // Add data rows for this account type
      groupedData[accountType].forEach(item => {
        const rowData = months.map(month => item[month] || '0');
        
        // Check if this is a total row (contains "Total" in account_name)
        const isTotal = item.account_name.toLowerCase().includes('total');
        
        tableData.push({
          label: item.account_name,
          data: rowData,
          isTotal: isTotal,
          indented: !isTotal && !item.account_name.toLowerCase().includes('total')
        });
      });
    });

    return { months, tableData };
  };

  // Transform the API data
  const { months, tableData } = transformApiData(reportData);

  // Fallback to empty state if no data
  if (!reportData || !reportData.profitAndLossMonthsTrailing || months.length === 0) {
    return (
      <div className="p-5 section profit-loss-section">
        <div className="max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto  report-container">
          <div className="report-header">
            <h4 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              Profit and Loss
            </h4>
            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              13 Month Trailing
            </div>
            <div style={{ ...contentTextStyle, fontSize: "20px" }}>
              Acme Print
            </div>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-lg text-gray-600">No data available</div>
              <div className="text-sm text-gray-500 mt-2">Please check your data source</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderTableRow = (item, index) => {
    if (item.isHeader) {
      return (
        <tr key={index} className="font-bold text-gray-800">
          <td className="text-left pl-2 font-normal" style={contentTextStyle}>
            <strong>{item.category}</strong>
          </td>
          {months.map((_, i) => (
            <td 
              key={i}
              style={{
                backgroundColor: i === months.length - 1 ? '#d2e9ea' : 'transparent'
              }}
            ></td>
          ))}
        </tr>
      );
    }

    if (item.isTotal) {
      return (
        <tr key={index} className="border-t-2 border-gray">
          <td className="text-left pl-2 font-normal" style={contentTextStyle}>
            <strong>{item.label}</strong>
          </td>
          {item.data.map((value, i) => (
            <td 
              key={i} 
              className="text-right font-mono" 
              style={{
                ...contentTextStyle,
                backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'
              }}
            >
              <strong>{value}</strong>
            </td>
          ))}
        </tr>
      );
    }

    return (
      <tr key={index}>
        <td
          className={`text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`}
          style={contentTextStyle}
        >
          {item.label}
        </td>
        {item.data.map((value, i) => (
          <td 
            key={i} 
            className="text-right font-mono" 
            style={{
              ...contentTextStyle,
              backgroundColor: i === item.data.length - 1 ? '#d2e9ea' : 'transparent'
            }}
          >
            {value}
          </td>
        ))}
      </tr>
    );
  };

  return (
    <div className="p-5 section profit-loss-section">
      {/* Main Container */}
      <div className="max-w-8xl mx-auto bg-white p-10 mx-auto overflow-x-auto  report-container">
        {/* Header Section */}
        <div className="report-header">
          <h4
            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}
          >
            Profit and Loss
          </h4>
          <div
            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}
          >
            13 Month Trailing
          </div>
          <div
            style={{ ...contentTextStyle, fontSize: "20px" }}
          >
            Acme Print
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse text-sm mt-4 profit-loss-table">
            <thead className="table-header-group">
              <tr className="header-row">
                <th
                  className="month-header"
                  style={{
                    width: '200px',
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    color: 'white'
                  }}
                >
                 
                </th>
                {months.map((month, index) => (
                  <th
                    key={index}
                    className="text-right text-white p-2 font-bold text-sm month-header"
                    style={{
                      backgroundColor: headerBgColor,
                      ...contentTextStyle,
                      color: 'white'
                    }}
                  >
                    {month}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="table-row-group">
              {/* Render all transformed data */}
              {tableData.map((item, index) => renderTableRow(item, `data-${index}`))}
            </tbody>
          </table>
        </div>
        
        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>
          <p>
            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice
            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any
            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or
            delivered information.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProfitLoss13MonthDashboard;